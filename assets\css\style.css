/* ===== CSS VARIABLES ===== */
:root {
  /* Colors */
  --primary-color: #2563eb;
  --primary-dark: #1d4ed8;
  --secondary-color: #64748b;
  --accent-color: #0ea5e9;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-light: #94a3b8;
  --background: #ffffff;
  --background-secondary: #f8fafc;
  --border-color: #e2e8f0;
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* Dark mode variables */
[data-theme="dark"] {
  --primary-color: #3b82f6;
  --primary-dark: #2563eb;
  --secondary-color: #94a3b8;
  --accent-color: #06b6d4;
  --text-primary: #f1f5f9;
  --text-secondary: #cbd5e1;
  --text-light: #64748b;
  --background: #0f172a;
  --background-secondary: #1e293b;
  --border-color: #334155;
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3);
}

:root {
  /* Typography */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;

  /* Spacing */
  --spacing-xs: 0.5rem;
  --spacing-sm: 0.75rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;

  /* Layout */
  --max-width: 1200px;
  --border-radius: 0.5rem;
  --border-radius-lg: 1rem;

  /* Transitions */
  --transition: all 0.3s ease;
}

/* ===== BASE STYLES ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--background);
  overflow-x: hidden;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* ===== UTILITY CLASSES ===== */
.container {
  max-width: var(--max-width);
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.section-title {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  text-align: center;
  margin-bottom: var(--spacing-3xl);
  color: var(--text-primary);
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
  border-radius: 2px;
}

.highlight {
  color: var(--primary-color);
  font-weight: 600;
}

/* ===== BUTTONS ===== */
.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--border-radius);
  font-weight: 500;
  text-decoration: none;
  transition: var(--transition);
  cursor: pointer;
  border: none;
  font-size: var(--font-size-sm);
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background-color: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
  background-color: var(--primary-color);
  color: white;
  transform: translateY(-2px);
}

/* ===== NAVIGATION ===== */
.nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: var(--background);
  backdrop-filter: blur(10px);
  z-index: 1000;
  padding: var(--spacing-md) 0;
  transition: var(--transition);
  border-bottom: 1px solid var(--border-color);
}

.nav-container {
  max-width: var(--max-width);
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--text-primary);
  text-decoration: none;
}

.nav-logo-img {
  width: 32px;
  height: 32px;
  border-radius: 50%;
}

.nav-menu {
  display: flex;
  gap: var(--spacing-xl);
}

.nav-link {
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 500;
  transition: var(--transition);
  position: relative;
}

.nav-link:hover,
.nav-link.active {
  color: var(--primary-color);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--primary-color);
  transition: var(--transition);
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 100%;
}

.nav-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
}

.bar {
  width: 25px;
  height: 3px;
  background-color: var(--text-primary);
  margin: 3px 0;
  transition: var(--transition);
}

/* ===== THEME TOGGLE ===== */
.theme-toggle {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: var(--font-size-lg);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: 50%;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
}

.theme-toggle:hover {
  background-color: var(--background-secondary);
  color: var(--primary-color);
}

/* ===== HERO SECTION ===== */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  padding-top: 80px;
  background-color: var(--background);
}

.hero-container {
  max-width: var(--max-width);
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-3xl);
  align-items: center;
}

.hero-content {
  /* Animation handled by ScrollReveal */
}

.hero-title {
  font-size: var(--font-size-5xl);
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: var(--spacing-lg);
  color: var(--text-primary);
}

.hero-subtitle {
  font-size: var(--font-size-xl);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-2xl);
  line-height: 1.6;
}

.hero-buttons {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-2xl);
}

.hero-social {
  display: flex;
  gap: var(--spacing-md);
}

.hero-social a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: var(--background-secondary);
  color: var(--text-secondary);
  border-radius: 50%;
  text-decoration: none;
  transition: var(--transition);
}

.hero-social a:hover {
  background-color: var(--primary-color);
  color: white;
  transform: translateY(-2px);
}

.hero-image {
  text-align: center;
  /* Animation handled by ScrollReveal */
}

.profile-img {
  width: 280px;
  height: 350px;
  border-radius: var(--border-radius-lg);
  object-fit: cover;
  box-shadow: var(--shadow-lg);
  transition: var(--transition);
}

.profile-img:hover {
  transform: scale(1.05);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* ===== ABOUT SECTION ===== */
.about {
  padding: var(--spacing-3xl) 0;
  background-color: var(--background);
}

.about-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-3xl);
  align-items: start;
}

.about-text {
  max-width: 600px;
}

.about-description {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
  line-height: 1.7;
  text-align: justify;
}

.about-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  margin-top: var(--spacing-xl);
}

.info-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--text-secondary);
}

.info-item i {
  color: var(--primary-color);
  width: 20px;
}

.about-skills h3 {
  font-size: var(--font-size-xl);
  margin-bottom: var(--spacing-lg);
  color: var(--text-primary);
}

.skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: var(--spacing-md);
}

.skill-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-md);
  background-color: var(--background-secondary);
  border-radius: var(--border-radius);
  transition: var(--transition);
  text-align: center;
}

.skill-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow);
}

.skill-item img {
  width: 40px;
  height: 40px;
  margin-bottom: var(--spacing-xs);
}

.skill-item span {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  font-weight: 500;
}

/* ===== PROJECTS SECTION ===== */
.projects {
  padding: var(--spacing-3xl) 0;
  background-color: var(--background-secondary);
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-lg);
  max-width: 1200px;
  margin: 0 auto;
}

.project-card {
  background-color: var(--background);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow);
  transition: var(--transition);
  height: 480px;
  display: flex;
  flex-direction: column;
}

.project-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-lg);
}

.project-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
  position: relative;
  background-color: var(--background-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
}

.project-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transition: transform 0.3s ease;
  display: block;
}

.project-card:hover .project-image img {
  transform: scale(1.05);
}

/* Fallback para imágenes que no cargan */
.project-image::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  opacity: 0.1;
  z-index: 1;
}

.project-image::after {
  content: '🖼️';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 2rem;
  color: var(--text-secondary);
  z-index: 2;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.project-image img[src=""],
.project-image img:not([src]) {
  opacity: 0;
}

.project-image:has(img[src=""]) ::after,
.project-image:has(img:not([src])) ::after {
  opacity: 1;
}

.project-content {
  padding: var(--spacing-lg);
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.project-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.project-description {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
  line-height: 1.6;
  text-align: justify;
}

.project-links {
  display: flex;
  gap: var(--spacing-sm);
}

.project-link {
  padding: var(--spacing-xs) var(--spacing-md);
  background-color: var(--primary-color);
  color: white;
  text-decoration: none;
  border-radius: var(--border-radius);
  font-size: var(--font-size-sm);
  transition: var(--transition);
}

.project-link:hover {
  background-color: var(--primary-dark);
}

.project-link.secondary {
  background-color: transparent;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.project-link.secondary:hover {
  background-color: var(--primary-color);
  color: white;
}

.projects-cta {
  text-align: center;
  margin-top: var(--spacing-2xl);
}

.projects-cta .btn {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-lg);
  font-weight: 600;
}



/* ===== EXPERIENCE SECTION ===== */
.experience {
  padding: var(--spacing-3xl) 0;
  background-color: var(--background-secondary);
}

.experience-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--spacing-2xl);
  max-width: 1000px;
  margin: 0 auto;
}

.experience-card {
  background-color: var(--background);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-2xl);
  box-shadow: var(--shadow);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.experience-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
}

.experience-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-lg);
}

.experience-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

.experience-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--font-size-xl);
}

.experience-period {
  background-color: var(--background-secondary);
  color: var(--primary-color);
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--border-radius);
  font-weight: 600;
  font-size: var(--font-size-lg);
}

.experience-content h3 {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.experience-content h4 {
  font-size: var(--font-size-lg);
  color: var(--primary-color);
  font-weight: 600;
  margin-bottom: var(--spacing-xs);
}

.experience-duration {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  font-weight: 500;
  margin-bottom: var(--spacing-md);
}

.experience-description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--spacing-lg);
  text-align: justify;
}

.experience-skills {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.skill-tag {
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  color: white;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  font-size: var(--font-size-xs);
  font-weight: 500;
}

/* ===== EDUCATION SECTION ===== */
.education {
  padding: var(--spacing-3xl) 0;
  background-color: var(--background);
}

.education-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--spacing-2xl);
  max-width: 1000px;
  margin: 0 auto;
}

.education-card {
  background-color: var(--background-secondary);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-2xl);
  box-shadow: var(--shadow);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.education-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, var(--accent-color), var(--primary-color));
}

.education-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-lg);
}

.education-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

.education-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--accent-color), var(--primary-color));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--font-size-xl);
}

.education-period {
  background-color: var(--background);
  color: var(--primary-color);
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--border-radius);
  font-weight: 600;
  font-size: var(--font-size-lg);
}

.education-content h3 {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.education-content h4 {
  font-size: var(--font-size-lg);
  color: var(--primary-color);
  font-weight: 600;
  margin-bottom: var(--spacing-md);
}

.education-institution {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--text-secondary);
  font-weight: 500;
  margin-bottom: var(--spacing-md);
}

.education-institution i {
  color: var(--primary-color);
  font-size: var(--font-size-base);
}

.education-description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--spacing-lg);
  text-align: justify;
}

.education-skills {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

/* ===== CONTACT SECTION ===== */
.contact {
  padding: var(--spacing-3xl) 0;
  background-color: var(--background-secondary);
}

.contact-content {
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
}

.contact-info p {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xl);
}

.contact-details {
  display: flex;
  justify-content: center;
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
}

.contact-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--text-secondary);
}

.contact-item i {
  color: var(--primary-color);
  font-size: var(--font-size-lg);
}

.contact-item a {
  color: var(--text-secondary);
  text-decoration: none;
  transition: var(--transition);
}

.contact-item a:hover {
  color: var(--primary-color);
}

.contact-social {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
}

.contact-social a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background-color: var(--background);
  color: var(--text-secondary);
  border-radius: 50%;
  text-decoration: none;
  transition: var(--transition);
  font-size: var(--font-size-lg);
}

.contact-social a:hover {
  background-color: var(--primary-color);
  color: white;
  transform: translateY(-3px);
}

/* ===== FOOTER ===== */
.footer {
  background-color: var(--background-secondary);
  color: var(--text-secondary);
  padding: var(--spacing-xl) 0;
  text-align: center;
  border-top: 1px solid var(--border-color);
}

.footer p {
  font-size: var(--font-size-sm);
}

/* ===== SCROLL TO TOP ===== */
.scroll-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-lg);
  transition: var(--transition);
  opacity: 0;
  visibility: hidden;
  z-index: 1000;
}

.scroll-top.show {
  opacity: 1;
  visibility: visible;
}

.scroll-top:hover {
  background-color: var(--primary-dark);
  transform: translateY(-3px);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  /* Navigation */
  .nav-menu {
    position: fixed;
    top: 70px;
    left: -100%;
    width: 100%;
    height: calc(100vh - 70px);
    background-color: var(--background);
    backdrop-filter: blur(10px);
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding-top: var(--spacing-3xl);
    transition: var(--transition);
    border-top: 1px solid var(--border-color);
  }

  .nav-menu.active {
    left: 0;
  }

  .nav-toggle {
    display: flex;
  }

  .theme-toggle {
    order: -1;
    margin-right: var(--spacing-md);
  }

  .nav-toggle.active .bar:nth-child(2) {
    opacity: 0;
  }

  .nav-toggle.active .bar:nth-child(1) {
    transform: translateY(8px) rotate(45deg);
  }

  .nav-toggle.active .bar:nth-child(3) {
    transform: translateY(-8px) rotate(-45deg);
  }

  /* Hero */
  .hero-container {
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
    text-align: center;
  }

  .hero-title {
    font-size: var(--font-size-4xl);
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }

  .profile-img {
    width: 220px;
    height: 280px;
  }

  /* About */
  .about-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
  }

  .skills-grid {
    grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
  }

  /* Projects */
  .projects-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  /* Experience */
  .experience-grid {
    grid-template-columns: 1fr;
  }

  .experience-card {
    padding: var(--spacing-lg);
  }

  .experience-header {
    flex-direction: column;
    gap: var(--spacing-md);
    text-align: center;
  }

  /* Education */
  .education-grid {
    grid-template-columns: 1fr;
  }

  .education-card {
    padding: var(--spacing-lg);
  }

  .education-header {
    flex-direction: column;
    gap: var(--spacing-md);
    text-align: center;
  }

  /* Contact */
  .contact-details {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  /* Utility */
  .container {
    padding: 0 var(--spacing-md);
  }

  .section-title {
    font-size: var(--font-size-2xl);
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: var(--font-size-3xl);
  }

  .hero-subtitle {
    font-size: var(--font-size-base);
  }

  .profile-img {
    width: 180px;
    height: 230px;
  }

  .btn {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--font-size-xs);
  }

  .skills-grid {
    grid-template-columns: repeat(auto-fit, minmax(50px, 1fr));
  }

  .skill-item img {
    width: 30px;
    height: 30px;
  }
}

/* ===== ANIMATIONS ===== */
/* All animations are now handled by ScrollReveal for better performance and consistency */
